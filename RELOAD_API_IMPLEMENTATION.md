# Google Ads API Reload Implementation

This document describes the implementation of reload functionality for Ads and Keywords modules, following the same pattern as the existing Campaigns and AdGroups modules.

## 📋 Overview

I have implemented reload functionality for both Ads and Keywords modules, following the exact same pattern as the existing Campaigns and AdGroups modules. The implementation includes:

### For Ads Module:
- **Reload Request**: `modules/googleads/ads/transport/requests/reload_ad_req.go`
- **Entity**: `modules/googleads/ads/entity/ad_entity.go`
- **Enums**: `modules/googleads/ads/common/enums/enums.go`
- **Mapping**: `modules/googleads/ads/mapping/mapping_upsert_ad_to_db.go`
- **Repository**: Updated `modules/googleads/ads/repository/mongo/ad_repo.go`
- **Usecase**: Updated `modules/googleads/ads/usecase/api_ad_usc.go`
- **API Handler**: `modules/googleads/ads/transport/api/reload_ad_api.go`
- **Router**: Updated `modules/googleads/ads/routes/router_ad.go`

### For Keywords Module:
- **Reload Request**: `modules/googleads/keywords/transport/requests/reload_keyword_req.go`
- **Entity**: `modules/googleads/keywords/entity/keyword_entity.go`
- **Enums**: `modules/googleads/keywords/common/enums/enums.go`
- **Mapping**: `modules/googleads/keywords/mapping/mapping_upsert_keyword_to_db.go`
- **Repository**: Updated `modules/googleads/keywords/repository/mongo/keyword_repo.go`
- **Usecase**: Updated `modules/googleads/keywords/usecase/api_keyword_usc.go`
- **API Handler**: `modules/googleads/keywords/transport/api/reload_keyword_api.go`
- **Router**: Updated `modules/googleads/keywords/routes/router_keyword.go`

## 🔗 API Endpoints

### Ads Reload API
**Endpoint**: `PATCH /dsp/googleads/api/ads/reload`

**Request Body**:
```json
{
  "advertiser_id": "string"
}
```

**Response**:
```json
{
  "data": {
    "msg": "Reload ads successfully"
  }
}
```

### Keywords Reload API
**Endpoint**: `PATCH /dsp/googleads/api/keywords/reload`

**Request Body**:
```json
{
  "advertiser_id": "string"
}
```

**Response**:
```json
{
  "data": {
    "msg": "Reload keywords successfully"
  }
}
```

## 🏗️ Implementation Details

### Pattern Consistency
The implementation follows the exact same pattern as campaigns and adgroups:

1. **Request Validation**: Both modules validate the `advertiser_id` field
2. **Service Integration**: Both modules use the `googleadsservice.Client` to fetch data from Google Ads API
3. **Bulk Operations**: Both modules use MongoDB bulk write operations for efficient data storage
4. **Error Handling**: Consistent error handling and logging throughout
5. **Response Format**: Standardized response format matching existing modules

### Key Components

#### Entities
- **AdEntity**: Represents ads with fields like ad_id, ad_name, ad_group_id, campaign_id, status, etc.
- **KeywordEntity**: Represents keywords with fields like keyword_id, keyword_text, ad_group_id, campaign_id, match_type, etc.

#### Enums
- **Ad Enums**: AdStatusEnum, AdPrimaryStatusEnum, AdTypeEnum
- **Keyword Enums**: KeywordStatusEnum, KeywordPrimaryStatusEnum, KeywordMatchTypeEnum

#### Service Methods
The implementation assumes the following methods exist in `googleadsservice.Client`:
- `ListAds(ctx context.Context, advertiserID string, options interface{}) ([]*resources.Ad, error)`
- `ListKeywords(ctx context.Context, advertiserID string, options interface{}) ([]*resources.AdGroupCriterion, error)`

#### Database Collections
- **Ads**: `googleads_ads`
- **Keywords**: `googleads_keywords`

## 🔧 Usage

### Testing the APIs

You can test the reload functionality using curl or any HTTP client:

```bash
# Reload Ads
curl -X PATCH http://localhost:8080/dsp/googleads/api/ads/reload \
  -H "Content-Type: application/json" \
  -d '{"advertiser_id": "your_advertiser_id"}'

# Reload Keywords
curl -X PATCH http://localhost:8080/dsp/googleads/api/keywords/reload \
  -H "Content-Type: application/json" \
  -d '{"advertiser_id": "your_advertiser_id"}'
```

## 📝 Notes

1. **Service Methods**: The implementation currently uses placeholder methods for `ListAd` and `ListKeyword` in the `googleadsservice.Client`. These need to be replaced with the actual service method names once they are identified.

2. **Data Mapping**: The mapping functions use basic field mappings. You may need to adjust the field mappings based on the actual structure of the Google Ads API responses.

3. **Error Handling**: All errors are logged and returned to the client with appropriate HTTP status codes.

4. **Validation**: Both modules validate that the `advertiser_id` is provided in the request.

5. **Consistency**: The implementation maintains consistency with the existing codebase patterns and follows the same architectural principles.

6. **Compilation Status**: ✅ **The code compiles successfully and the server runs without errors.**

## 🚀 Next Steps

1. **Identify Service Methods**: Find the correct method names in `googleadsservice.Client` for listing ads and keywords (similar to `ListCampaignStream` and `ListAdGroup`).

2. **Update Service Calls**: Replace the placeholder implementations in:
   - `modules/googleads/ads/usecase/api_ad_usc.go` (line ~105)
   - `modules/googleads/keywords/usecase/api_keyword_usc.go` (line ~118)

3. **Adjust Mappings**: Fine-tune the data mappings in:
   - `modules/googleads/ads/mapping/mapping_upsert_ad_to_db.go`
   - `modules/googleads/keywords/mapping/mapping_upsert_keyword_to_db.go`

4. **Test the APIs**: Once the service methods are implemented, test the reload endpoints:
   ```bash
   curl -X PATCH http://localhost:3008/dsp/googleads/api/ads/reload \
     -H "Content-Type: application/json" \
     -d '{"advertiser_id": "your_advertiser_id"}'
   ```

5. **Add Tests**: Consider adding unit tests for the new functionality.

6. **Documentation**: Update any API documentation to include the new reload endpoints.
