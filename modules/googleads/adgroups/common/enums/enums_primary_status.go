package enums

type AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus int32

const (
	// Not specified.
	AdGroupPrimaryStatusEnum_UNSPECIFIED AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus = 0
	// Used for return value only. Represents value unknown in this version.
	AdGroupPrimaryStatusEnum_UNKNOWN AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus = 1
	// The ad group is eligible to serve.
	AdGroupPrimaryStatusEnum_ELIGIBLE AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus = 2
	// The ad group is paused.
	AdGroupPrimaryStatusEnum_PAUSED AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus = 3
	// The ad group is removed.
	AdGroupPrimaryStatusEnum_REMOVED AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus = 4
	// The ad group may serve in the future.
	AdGroupPrimaryStatusEnum_PENDING AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus = 5
	// The ad group is not eligible to serve.
	AdGroupPrimaryStatusEnum_NOT_ELIGIBLE AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus = 6
	// The ad group has limited servability.
	AdGroupPrimaryStatusEnum_LIMITED AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus = 7
)

var (
	AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		2: "ELIGIBLE",
		3: "PAUSED",
		4: "REMOVED",
		5: "PENDING",
		6: "NOT_ELIGIBLE",
		7: "LIMITED",
	}
	AdGroupPrimaryStatusEnum_AdGroupPrimaryStatus_value = map[string]int32{
		"UNSPECIFIED":  0,
		"UNKNOWN":      1,
		"ELIGIBLE":     2,
		"PAUSED":       3,
		"REMOVED":      4,
		"PENDING":      5,
		"NOT_ELIGIBLE": 6,
		"LIMITED":      7,
	}
)
