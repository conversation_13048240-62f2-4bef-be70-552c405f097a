package enums

type BiddingSourceEnum_BiddingSource int32

const (
	// Not specified.
	BiddingSourceEnum_UNSPECIFIED BiddingSourceEnum_BiddingSource = 0
	// Used for return value only. Represents value unknown in this version.
	BiddingSourceEnum_UNKNOWN BiddingSourceEnum_BiddingSource = 1
	// Effective bid or target is inherited from campaign bidding strategy.
	BiddingSourceEnum_CAMPAIGN_BIDDING_STRATEGY BiddingSourceEnum_BiddingSource = 5
	// The bid or target is defined on the ad group.
	BiddingSourceEnum_AD_GROUP BiddingSourceEnum_BiddingSource = 6
	// The bid or target is defined on the ad group criterion.
	BiddingSourceEnum_AD_GROUP_CRITERION BiddingSourceEnum_BiddingSource = 7
)

var (
	BiddingSourceEnum_BiddingSource_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		5: "CAMPAIGN_BIDDING_STRATEGY",
		6: "AD_GROUP",
		7: "AD_GROUP_CRITERION",
	}
	BiddingSourceEnum_BiddingSource_value = map[string]int32{
		"UNSPECIFIED":               0,
		"UNKNOWN":                   1,
		"CAMPAIGN_BIDDING_STRATEGY": 5,
		"AD_GROUP":                  6,
		"AD_GROUP_CRITERION":        7,
	}
)
