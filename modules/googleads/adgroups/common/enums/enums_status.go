package enums

type AdGroupStatusEnum_AdGroupStatus int32

const (
	// The status has not been specified.
	AdGroupStatusEnum_UNSPECIFIED AdGroupStatusEnum_AdGroupStatus = 0
	// The received value is not known in this version.
	//
	// This is a response-only value.
	AdGroupStatusEnum_UNKNOWN AdGroupStatusEnum_AdGroupStatus = 1
	// The ad group is enabled.
	AdGroupStatusEnum_ENABLED AdGroupStatusEnum_AdGroupStatus = 2
	// The ad group is paused.
	AdGroupStatusEnum_PAUSED AdGroupStatusEnum_AdGroupStatus = 3
	// The ad group is removed.
	AdGroupStatusEnum_REMOVED AdGroupStatusEnum_AdGroupStatus = 4
)

var (
	AdGroupStatusEnum_AdGroupStatus_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		2: "ENABLED",
		3: "PAUSED",
		4: "REMOVED",
	}
	AdGroupStatusEnum_AdGroupStatus_value = map[string]int32{
		"UNSPECIFIED": 0,
		"UNKNOWN":     1,
		"ENABLED":     2,
		"PAUSED":      3,
		"REMOVED":     4,
	}
)
