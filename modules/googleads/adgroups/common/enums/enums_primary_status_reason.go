package enums

type AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason int32

const (
	// Not specified.
	AdGroupPrimaryStatusReasonEnum_UNSPECIFIED AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 0
	// Used for return value only. Represents value unknown in this version.
	AdGroupPrimaryStatusReasonEnum_UNKNOWN AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 1
	// The user-specified campaign status is removed. Contributes to
	// AdGroupPrimaryStatus.NOT_ELIGIBLE.
	AdGroupPrimaryStatusReasonEnum_CAMPAIGN_REMOVED AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 2
	// The user-specified campaign status is paused. Contributes to
	// AdGroupPrimaryStatus.NOT_ELIGIBLE.
	AdGroupPrimaryStatusReasonEnum_CAMPAIGN_PAUSED AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 3
	// The user-specified time for this campaign to start is in the future.
	// Contributes to AdGroupPrimaryStatus.NOT_ELIGIBLE
	AdGroupPrimaryStatusReasonEnum_CAMPAIGN_PENDING AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 4
	// The user-specified time for this campaign to end has passed. Contributes
	// to AdGroupPrimaryStatus.NOT_ELIGIBLE.
	AdGroupPrimaryStatusReasonEnum_CAMPAIGN_ENDED AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 5
	// The user-specified ad group status is paused. Contributes to
	// AdGroupPrimaryStatus.PAUSED.
	AdGroupPrimaryStatusReasonEnum_AD_GROUP_PAUSED AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 6
	// The user-specified ad group status is removed. Contributes to
	// AdGroupPrimaryStatus.REMOVED.
	AdGroupPrimaryStatusReasonEnum_AD_GROUP_REMOVED AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 7
	// The construction of this ad group is not yet complete. Contributes to
	// AdGroupPrimaryStatus.NOT_ELIGIBLE.
	AdGroupPrimaryStatusReasonEnum_AD_GROUP_INCOMPLETE AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 8
	// The user-specified keyword statuses in this ad group are all paused.
	// Contributes to AdGroupPrimaryStatus.NOT_ELIGIBLE.
	AdGroupPrimaryStatusReasonEnum_KEYWORDS_PAUSED AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 9
	// No eligible keywords exist in this ad group. Contributes to
	// AdGroupPrimaryStatus.NOT_ELIGIBLE.
	AdGroupPrimaryStatusReasonEnum_NO_KEYWORDS AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 10
	// The user-specified ad group ads statuses in this ad group are all paused.
	// Contributes to AdGroupPrimaryStatus.NOT_ELIGIBLE.
	AdGroupPrimaryStatusReasonEnum_AD_GROUP_ADS_PAUSED AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 11
	// No eligible ad group ads exist in this ad group. Contributes to
	// AdGroupPrimaryStatus.NOT_ELIGIBLE.
	AdGroupPrimaryStatusReasonEnum_NO_AD_GROUP_ADS AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 12
	// Policy status reason when at least one ad is disapproved. Contributes to
	// multiple AdGroupPrimaryStatus.
	AdGroupPrimaryStatusReasonEnum_HAS_ADS_DISAPPROVED AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 13
	// Policy status reason when at least one ad is limited by policy.
	// Contributes to multiple AdGroupPrimaryStatus.
	AdGroupPrimaryStatusReasonEnum_HAS_ADS_LIMITED_BY_POLICY AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 14
	// Policy status reason when most ads are pending review. Contributes to
	// AdGroupPrimaryStatus.PENDING.
	AdGroupPrimaryStatusReasonEnum_MOST_ADS_UNDER_REVIEW AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 15
	// The AdGroup belongs to a Draft campaign. Contributes to
	// AdGroupPrimaryStatus.NOT_ELIGIBLE.
	AdGroupPrimaryStatusReasonEnum_CAMPAIGN_DRAFT AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 16
	// Ad group has been paused due to prolonged low activity in serving.
	// Contributes to AdGroupPrimaryStatus.PAUSED.
	AdGroupPrimaryStatusReasonEnum_AD_GROUP_PAUSED_DUE_TO_LOW_ACTIVITY AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason = 19
)

var (
	AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason_name = map[int32]string{
		0:  "UNSPECIFIED",
		1:  "UNKNOWN",
		2:  "CAMPAIGN_REMOVED",
		3:  "CAMPAIGN_PAUSED",
		4:  "CAMPAIGN_PENDING",
		5:  "CAMPAIGN_ENDED",
		6:  "AD_GROUP_PAUSED",
		7:  "AD_GROUP_REMOVED",
		8:  "AD_GROUP_INCOMPLETE",
		9:  "KEYWORDS_PAUSED",
		10: "NO_KEYWORDS",
		11: "AD_GROUP_ADS_PAUSED",
		12: "NO_AD_GROUP_ADS",
		13: "HAS_ADS_DISAPPROVED",
		14: "HAS_ADS_LIMITED_BY_POLICY",
		15: "MOST_ADS_UNDER_REVIEW",
		16: "CAMPAIGN_DRAFT",
		19: "AD_GROUP_PAUSED_DUE_TO_LOW_ACTIVITY",
	}
	AdGroupPrimaryStatusReasonEnum_AdGroupPrimaryStatusReason_value = map[string]int32{
		"UNSPECIFIED":                         0,
		"UNKNOWN":                             1,
		"CAMPAIGN_REMOVED":                    2,
		"CAMPAIGN_PAUSED":                     3,
		"CAMPAIGN_PENDING":                    4,
		"CAMPAIGN_ENDED":                      5,
		"AD_GROUP_PAUSED":                     6,
		"AD_GROUP_REMOVED":                    7,
		"AD_GROUP_INCOMPLETE":                 8,
		"KEYWORDS_PAUSED":                     9,
		"NO_KEYWORDS":                         10,
		"AD_GROUP_ADS_PAUSED":                 11,
		"NO_AD_GROUP_ADS":                     12,
		"HAS_ADS_DISAPPROVED":                 13,
		"HAS_ADS_LIMITED_BY_POLICY":           14,
		"MOST_ADS_UNDER_REVIEW":               15,
		"CAMPAIGN_DRAFT":                      16,
		"AD_GROUP_PAUSED_DUE_TO_LOW_ACTIVITY": 19,
	}
)
