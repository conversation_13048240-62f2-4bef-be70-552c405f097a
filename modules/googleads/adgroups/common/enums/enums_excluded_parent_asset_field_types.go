package enums

type AssetFieldTypeEnum_AssetFieldType int32

const (
	// Not specified.
	AssetFieldTypeEnum_UNSPECIFIED AssetFieldTypeEnum_AssetFieldType = 0
	// Used for return value only. Represents value unknown in this version.
	AssetFieldTypeEnum_UNKNOWN AssetFieldTypeEnum_AssetFieldType = 1
	// The asset is linked for use as a headline.
	AssetFieldTypeEnum_HEADLINE AssetFieldTypeEnum_AssetFieldType = 2
	// The asset is linked for use as a description.
	AssetFieldTypeEnum_DESCRIPTION AssetFieldTypeEnum_AssetFieldType = 3
	// The asset is linked for use as mandatory ad text.
	AssetFieldTypeEnum_MANDATORY_AD_TEXT AssetFieldTypeEnum_AssetFieldType = 4
	// The asset is linked for use as a marketing image.
	AssetFieldTypeEnum_MARKETING_IMAGE AssetFieldTypeEnum_AssetFieldType = 5
	// The asset is linked for use as a media bundle.
	AssetFieldTypeEnum_MEDIA_BUNDLE AssetFieldTypeEnum_AssetFieldType = 6
	// The asset is linked for use as a YouTube video.
	AssetFieldTypeEnum_YOUTUBE_VIDEO AssetFieldTypeEnum_AssetFieldType = 7
	// The asset is linked to indicate that a hotels campaign is "Book on
	// Google" enabled.
	AssetFieldTypeEnum_BOOK_ON_GOOGLE AssetFieldTypeEnum_AssetFieldType = 8
	// The asset is linked for use as a Lead Form extension.
	AssetFieldTypeEnum_LEAD_FORM AssetFieldTypeEnum_AssetFieldType = 9
	// The asset is linked for use as a Promotion extension.
	AssetFieldTypeEnum_PROMOTION AssetFieldTypeEnum_AssetFieldType = 10
	// The asset is linked for use as a Callout extension.
	AssetFieldTypeEnum_CALLOUT AssetFieldTypeEnum_AssetFieldType = 11
	// The asset is linked for use as a Structured Snippet extension.
	AssetFieldTypeEnum_STRUCTURED_SNIPPET AssetFieldTypeEnum_AssetFieldType = 12
	// The asset is linked for use as a Sitelink.
	AssetFieldTypeEnum_SITELINK AssetFieldTypeEnum_AssetFieldType = 13
	// The asset is linked for use as a Mobile App extension.
	AssetFieldTypeEnum_MOBILE_APP AssetFieldTypeEnum_AssetFieldType = 14
	// The asset is linked for use as a Hotel Callout extension.
	AssetFieldTypeEnum_HOTEL_CALLOUT AssetFieldTypeEnum_AssetFieldType = 15
	// The asset is linked for use as a Call extension.
	AssetFieldTypeEnum_CALL AssetFieldTypeEnum_AssetFieldType = 16
	// The asset is linked for use as a Price extension.
	AssetFieldTypeEnum_PRICE AssetFieldTypeEnum_AssetFieldType = 24
	// The asset is linked for use as a long headline.
	AssetFieldTypeEnum_LONG_HEADLINE AssetFieldTypeEnum_AssetFieldType = 17
	// The asset is linked for use as a business name.
	AssetFieldTypeEnum_BUSINESS_NAME AssetFieldTypeEnum_AssetFieldType = 18
	// The asset is linked for use as a square marketing image.
	AssetFieldTypeEnum_SQUARE_MARKETING_IMAGE AssetFieldTypeEnum_AssetFieldType = 19
	// The asset is linked for use as a portrait marketing image.
	AssetFieldTypeEnum_PORTRAIT_MARKETING_IMAGE AssetFieldTypeEnum_AssetFieldType = 20
	// The asset is linked for use as a logo.
	AssetFieldTypeEnum_LOGO AssetFieldTypeEnum_AssetFieldType = 21
	// The asset is linked for use as a landscape logo.
	AssetFieldTypeEnum_LANDSCAPE_LOGO AssetFieldTypeEnum_AssetFieldType = 22
	// The asset is linked for use as a non YouTube logo.
	AssetFieldTypeEnum_VIDEO AssetFieldTypeEnum_AssetFieldType = 23
	// The asset is linked for use to select a call-to-action.
	AssetFieldTypeEnum_CALL_TO_ACTION_SELECTION AssetFieldTypeEnum_AssetFieldType = 25
	// The asset is linked for use to select an ad image.
	AssetFieldTypeEnum_AD_IMAGE AssetFieldTypeEnum_AssetFieldType = 26
	// The asset is linked for use as a business logo.
	AssetFieldTypeEnum_BUSINESS_LOGO AssetFieldTypeEnum_AssetFieldType = 27
	// The asset is linked for use as a hotel property in a Performance Max for
	// travel goals campaign.
	AssetFieldTypeEnum_HOTEL_PROPERTY AssetFieldTypeEnum_AssetFieldType = 28
	// The asset is linked for use as a Demand Gen carousel card.
	AssetFieldTypeEnum_DEMAND_GEN_CAROUSEL_CARD AssetFieldTypeEnum_AssetFieldType = 30
	// The asset is linked for use as a Business Message.
	AssetFieldTypeEnum_BUSINESS_MESSAGE AssetFieldTypeEnum_AssetFieldType = 31
	// The asset is linked for use as a tall portrait marketing image.
	AssetFieldTypeEnum_TALL_PORTRAIT_MARKETING_IMAGE AssetFieldTypeEnum_AssetFieldType = 32
)

var (
	AssetFieldTypeEnum_AssetFieldType_name = map[int32]string{
		0:  "UNSPECIFIED",
		1:  "UNKNOWN",
		2:  "HEADLINE",
		3:  "DESCRIPTION",
		4:  "MANDATORY_AD_TEXT",
		5:  "MARKETING_IMAGE",
		6:  "MEDIA_BUNDLE",
		7:  "YOUTUBE_VIDEO",
		8:  "BOOK_ON_GOOGLE",
		9:  "LEAD_FORM",
		10: "PROMOTION",
		11: "CALLOUT",
		12: "STRUCTURED_SNIPPET",
		13: "SITELINK",
		14: "MOBILE_APP",
		15: "HOTEL_CALLOUT",
		16: "CALL",
		24: "PRICE",
		17: "LONG_HEADLINE",
		18: "BUSINESS_NAME",
		19: "SQUARE_MARKETING_IMAGE",
		20: "PORTRAIT_MARKETING_IMAGE",
		21: "LOGO",
		22: "LANDSCAPE_LOGO",
		23: "VIDEO",
		25: "CALL_TO_ACTION_SELECTION",
		26: "AD_IMAGE",
		27: "BUSINESS_LOGO",
		28: "HOTEL_PROPERTY",
		30: "DEMAND_GEN_CAROUSEL_CARD",
		31: "BUSINESS_MESSAGE",
		32: "TALL_PORTRAIT_MARKETING_IMAGE",
	}
	AssetFieldTypeEnum_AssetFieldType_value = map[string]int32{
		"UNSPECIFIED":                   0,
		"UNKNOWN":                       1,
		"HEADLINE":                      2,
		"DESCRIPTION":                   3,
		"MANDATORY_AD_TEXT":             4,
		"MARKETING_IMAGE":               5,
		"MEDIA_BUNDLE":                  6,
		"YOUTUBE_VIDEO":                 7,
		"BOOK_ON_GOOGLE":                8,
		"LEAD_FORM":                     9,
		"PROMOTION":                     10,
		"CALLOUT":                       11,
		"STRUCTURED_SNIPPET":            12,
		"SITELINK":                      13,
		"MOBILE_APP":                    14,
		"HOTEL_CALLOUT":                 15,
		"CALL":                          16,
		"PRICE":                         24,
		"LONG_HEADLINE":                 17,
		"BUSINESS_NAME":                 18,
		"SQUARE_MARKETING_IMAGE":        19,
		"PORTRAIT_MARKETING_IMAGE":      20,
		"LOGO":                          21,
		"LANDSCAPE_LOGO":                22,
		"VIDEO":                         23,
		"CALL_TO_ACTION_SELECTION":      25,
		"AD_IMAGE":                      26,
		"BUSINESS_LOGO":                 27,
		"HOTEL_PROPERTY":                28,
		"DEMAND_GEN_CAROUSEL_CARD":      30,
		"BUSINESS_MESSAGE":              31,
		"TALL_PORTRAIT_MARKETING_IMAGE": 32,
	}
)
