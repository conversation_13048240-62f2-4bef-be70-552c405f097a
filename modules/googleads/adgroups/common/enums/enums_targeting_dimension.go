package enums

type TargetingDimensionEnum_TargetingDimension int32

const (
	// Not specified.
	TargetingDimensionEnum_UNSPECIFIED TargetingDimensionEnum_TargetingDimension = 0
	// Used for return value only. Represents value unknown in this version.
	TargetingDimensionEnum_UNKNOWN TargetingDimensionEnum_TargetingDimension = 1
	// Keyword criteria, for example, 'mars cruise'. KEYWORD may be used as a
	// custom bid dimension. Keywords are always a targeting dimension, so may
	// not be set as a target "ALL" dimension with TargetRestriction.
	TargetingDimensionEnum_KEYWORD TargetingDimensionEnum_TargetingDimension = 2
	// Audience criteria, which include user list, user interest, custom
	// affinity,  and custom in market.
	TargetingDimensionEnum_AUDIENCE TargetingDimensionEnum_TargetingDimension = 3
	// Topic criteria for targeting categories of content, for example,
	// 'category::Animals>Pets' Used for Display and Video targeting.
	TargetingDimensionEnum_TOPIC TargetingDimensionEnum_TargetingDimension = 4
	// Criteria for targeting gender.
	TargetingDimensionEnum_GENDER TargetingDimensionEnum_TargetingDimension = 5
	// Criteria for targeting age ranges.
	TargetingDimensionEnum_AGE_RANGE TargetingDimensionEnum_TargetingDimension = 6
	// Placement criteria, which include websites like 'www.flowers4sale.com',
	// as well as mobile applications, mobile app categories, YouTube videos,
	// and YouTube channels.
	TargetingDimensionEnum_PLACEMENT TargetingDimensionEnum_TargetingDimension = 7
	// Criteria for parental status targeting.
	TargetingDimensionEnum_PARENTAL_STATUS TargetingDimensionEnum_TargetingDimension = 8
	// Criteria for income range targeting.
	TargetingDimensionEnum_INCOME_RANGE TargetingDimensionEnum_TargetingDimension = 9
)

var (
	TargetingDimensionEnum_TargetingDimension_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		2: "KEYWORD",
		3: "AUDIENCE",
		4: "TOPIC",
		5: "GENDER",
		6: "AGE_RANGE",
		7: "PLACEMENT",
		8: "PARENTAL_STATUS",
		9: "INCOME_RANGE",
	}
	TargetingDimensionEnum_TargetingDimension_value = map[string]int32{
		"UNSPECIFIED":     0,
		"UNKNOWN":         1,
		"KEYWORD":         2,
		"AUDIENCE":        3,
		"TOPIC":           4,
		"GENDER":          5,
		"AGE_RANGE":       6,
		"PLACEMENT":       7,
		"PARENTAL_STATUS": 8,
		"INCOME_RANGE":    9,
	}
)
