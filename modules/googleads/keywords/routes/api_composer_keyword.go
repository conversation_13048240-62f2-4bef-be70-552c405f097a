package routes

import (
	"googledsp/conf"
	"googledsp/modules/googleads/keywords/repository/mongo"
	"googledsp/modules/googleads/keywords/transport/api"
	"googledsp/modules/googleads/keywords/usecase"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/dspgos/sctx/component/google/googleads"
	"github.com/dev-networldasia/dspgos/sctx/component/mongodb"
	"github.com/dev-networldasia/dspgos/sctx/configs"

	googleAdsReportR "googledsp/modules/googleads/adserver_report/repository/mongo"
)

func ComposerKeywordApiService(serviceCtx sctx.ServiceContext) api.KeywordApiInterface {
	mongoDB := serviceCtx.MustGet(configs.KeyCompMongoDB).(mongodb.MongoComponent).GetDatabase()
	mongoAdserverReportDB := serviceCtx.MustGet(conf.KeyCompReportMongoDB).(mongodb.MongoComponent).GetDatabase()
	googleAdsComp := serviceCtx.MustGet(configs.KeyGoogleAds).(googleads.GoogleAdsServices)
	service := googleAdsComp.GetGoogleAdsService()
	logger := serviceCtx.MustGet(configs.KeyLoggerDaily).(sctx.AppLoggerDaily).GetLogger("web")

	// Create repositories
	keywordRepo := mongo.NewKeywordRepo(mongoDB)
	googleAdsKeywordInsightsRepo := googleAdsReportR.NewGoogleAdKeywordInsightsRepo(mongoAdserverReportDB)

	param := usecase.ParamKeywordUsc{
		Service:                      service,
		KeywordRepo:                  keywordRepo,
		GoogleAdsKeywordInsightsRepo: googleAdsKeywordInsightsRepo,
		Logger:                       logger,
	}
	keywordUsc := usecase.NewKeywordUsc(param)
	keywordApi := api.NewApiKeyword(keywordUsc)
	return keywordApi
}
