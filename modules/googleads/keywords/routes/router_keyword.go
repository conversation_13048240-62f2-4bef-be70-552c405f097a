package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesKeyword(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {

	group := app.Group("dsp/googleads/keywords")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comPage := ComposerKeywordService(serviceCtx)
		group.Get("/list", comPage.ListKeywordHdl(store)).Name("googleads.keywords.list")
		group.Get("/edit", comPage.EditKeywordHdl(store)).Name("googleads.keywords.edit")
	}

	// API routes
	apiGroup := app.Group("dsp/googleads/api/keywords")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}

		comApi := ComposerKeywordApiService(serviceCtx)
		apiGroup.Post("/list-table", comApi.ListTableKeywordsApi()).Name("googleads.api.keywords.list-table")
		apiGroup.Patch("/reload", comApi.ReloadKeywordApi()).Name("googleads.api.keywords.reload")
	}
}
