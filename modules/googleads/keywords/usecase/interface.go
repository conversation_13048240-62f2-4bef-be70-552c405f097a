package usecase

import (
	"context"
	"googledsp/modules/googleads/adserver_report/entity"
	"googledsp/modules/googleads/keywords/transport/requests"
	"googledsp/modules/googleads/keywords/transport/responses"

	"github.com/shenzhencenter/google-ads-pb/resources"
	"go.mongodb.org/mongo-driver/bson"
)

type KeywordRepo interface {
	BulkWriteKeywordsRepo(ctx context.Context, keywords []*resources.AdGroupCriterion) error
}

type GoogleAdsKeywordInsightsRepo interface {
	GetKeywordInsights(ctx context.Context, filter bson.M) (*[]entity.GoogleAdKeywordInsightsEntity, error)
	GetKeywordInsightsWithPagination(ctx context.Context, filter bson.M, skip int64, limit int64) (*[]entity.GoogleAdKeywordInsightsEntity, error)
	GetKeywordCount(ctx context.Context, filter bson.M) (int64, error)
	GetKeywordListTableWithPagination(ctx context.Context, filter bson.M, skip int64, limit int64) (*[]entity.GoogleAdKeywordInsightsEntity, error)
}

type KeywordUscInterface interface {
	ListTableKeywords(ctx context.Context, req *requests.ListTableKeywordReq) (*[]responses.KeywordRow, int64, error)
	ReloadKeywords(ctx context.Context, payload *requests.ReloadKeywordReq) error
}
