package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"googledsp/modules/googleads/keywords/mapping"
	"googledsp/modules/googleads/keywords/transport/requests"
	"googledsp/modules/googleads/keywords/transport/responses"
	"time"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/googleadsservice"
	"go.mongodb.org/mongo-driver/bson"
)

type KeywordUsc struct {
	service                      *googleadsservice.Client
	keywordRepo                  KeywordRepo
	googleAdsKeywordInsightsRepo GoogleAdsKeywordInsightsRepo
	logger                       sctx.Logger
}

type ParamKeywordUsc struct {
	Service                      *googleadsservice.Client
	KeywordRepo                  KeywordRepo
	GoogleAdsKeywordInsightsRepo GoogleAdsKeywordInsightsRepo
	Logger                       sctx.Logger
}

func NewKeywordUsc(param ParamKeywordUsc) *KeywordUsc {
	return &KeywordUsc{
		service:                      param.Service,
		keywordRepo:                  param.KeywordRepo,
		googleAdsKeywordInsightsRepo: param.GoogleAdsKeywordInsightsRepo,
		logger:                       param.Logger,
	}
}

func (usc *KeywordUsc) getFilterKeywordReport(ctx context.Context, payload *requests.ListTableKeywordReq) bson.M {
	filter := bson.M{}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	if payload.KeywordId != "" {
		filter["ad_group_criterion_keyword_text"] = bson.M{"$regex": payload.KeywordId, "$options": "i"}
	}
	if payload.AdGroupId != "" {
		filter["ad_group_id"] = payload.AdGroupId
	}
	if payload.CampaignId != "" {
		filter["campaign_id"] = payload.CampaignId
	}
	if payload.AdvertiserID != "" {
		filter["advertiser_id"] = payload.AdvertiserID
	}

	// Add search functionality
	if payload.GetSearchValue() != "" {
		searchRegex := bson.M{"$regex": payload.GetSearchValue(), "$options": "i"}
		filter["$or"] = []bson.M{
			{"ad_group_criterion_keyword_text": searchRegex},
			{"campaign_name": searchRegex},
			{"ad_group_name": searchRegex},
		}
	}

	return filter
}

func (usc *KeywordUsc) ListTableKeywords(ctx context.Context, req *requests.ListTableKeywordReq) (*[]responses.KeywordRow, int64, error) {
	reportFilter := usc.getFilterKeywordReport(ctx, req)

	// Calculate pagination parameters
	skip := int64(req.Start)
	limit := int64(req.Length)

	// Get paginated keyword data
	keywordData, err := usc.googleAdsKeywordInsightsRepo.GetKeywordInsightsWithPagination(ctx, reportFilter, skip, limit)
	if err != nil {
		fmt.Printf("====== Keywords err ====== %+v", err)
		return nil, 0, err
	}

	// Get total count for pagination
	totalCount, err := usc.googleAdsKeywordInsightsRepo.GetKeywordCount(ctx, reportFilter)
	if err != nil {
		fmt.Printf("====== Keywords count err ====== %+v", err)
		return nil, 0, err
	}

	fmt.Printf("\n===== Keywords len ====== %+v, total: %+v ", len(*keywordData), totalCount)

	dataTableRes := mapping.MapperListTableKeyword(keywordData)

	return dataTableRes, totalCount, nil
}

/**
 * Reload keywords
 */
func (usc *KeywordUsc) ReloadKeywords(ctx context.Context, payload *requests.ReloadKeywordReq) error {
	keywords, err := usc.service.ListKeywords(ctx, payload.AdvertiserID, nil)
	if err != nil {
		usc.logger.Error(err)
		return err
	}

	jsonData, err := json.MarshalIndent(keywords, "", "  ")
	if err != nil {
		return err
	}
	fmt.Printf("======== %+v \n", string(jsonData))

	if keywords != nil && len(keywords) > 0 {
		err = usc.keywordRepo.BulkWriteKeywordsRepo(ctx, keywords)
		if err != nil {
			usc.logger.Error(err)
			return err
		}
	}

	return nil
}
