package enums

// KeywordStatusEnum_KeywordStatus represents the status of a keyword
type KeywordStatusEnum_KeywordStatus int32

const (
	KeywordStatusEnum_UNSPECIFIED KeywordStatusEnum_KeywordStatus = 0
	KeywordStatusEnum_UNKNOWN     KeywordStatusEnum_KeywordStatus = 1
	KeywordStatusEnum_ENABLED     KeywordStatusEnum_KeywordStatus = 2
	KeywordStatusEnum_PAUSED      KeywordStatusEnum_KeywordStatus = 3
	KeywordStatusEnum_REMOVED     KeywordStatusEnum_KeywordStatus = 4
)

// KeywordPrimaryStatusEnum_KeywordPrimaryStatus represents the primary status of a keyword
type KeywordPrimaryStatusEnum_KeywordPrimaryStatus int32

const (
	KeywordPrimaryStatusEnum_UNSPECIFIED KeywordPrimaryStatusEnum_KeywordPrimaryStatus = 0
	KeywordPrimaryStatusEnum_UNKNOWN     KeywordPrimaryStatusEnum_KeywordPrimaryStatus = 1
	KeywordPrimaryStatusEnum_ELIGIBLE    KeywordPrimaryStatusEnum_KeywordPrimaryStatus = 2
	KeywordPrimaryStatusEnum_PAUSED      KeywordPrimaryStatusEnum_KeywordPrimaryStatus = 3
	KeywordPrimaryStatusEnum_REMOVED     KeywordPrimaryStatusEnum_KeywordPrimaryStatus = 4
	KeywordPrimaryStatusEnum_PENDING     KeywordPrimaryStatusEnum_KeywordPrimaryStatus = 5
	KeywordPrimaryStatusEnum_LIMITED     KeywordPrimaryStatusEnum_KeywordPrimaryStatus = 6
	KeywordPrimaryStatusEnum_NOT_ELIGIBLE KeywordPrimaryStatusEnum_KeywordPrimaryStatus = 7
)

// KeywordMatchTypeEnum_KeywordMatchType represents the match type of a keyword
type KeywordMatchTypeEnum_KeywordMatchType int32

const (
	KeywordMatchTypeEnum_UNSPECIFIED KeywordMatchTypeEnum_KeywordMatchType = 0
	KeywordMatchTypeEnum_UNKNOWN     KeywordMatchTypeEnum_KeywordMatchType = 1
	KeywordMatchTypeEnum_EXACT       KeywordMatchTypeEnum_KeywordMatchType = 2
	KeywordMatchTypeEnum_PHRASE      KeywordMatchTypeEnum_KeywordMatchType = 3
	KeywordMatchTypeEnum_BROAD       KeywordMatchTypeEnum_KeywordMatchType = 4
)
