package mapping

import (
	"fmt"

	"github.com/shenzhencenter/google-ads-pb/resources"
	"go.mongodb.org/mongo-driver/bson"
)

func MapperUpsertKeywordToDatabase(keyword *resources.AdGroupCriterion) bson.M {
	var keywordID string
	if keyword.CriterionId != nil {
		keywordID = fmt.Sprintf("%d", *keyword.CriterionId)
	} else {
		keywordID = ""
	}

	var adGroupID string
	if keyword.AdGroup != nil {
		adGroupID = fmt.Sprintf("%d", *keyword.AdGroup)
	} else {
		adGroupID = ""
	}

	var campaignID string
	if keyword.Campaign != nil {
		campaignID = fmt.Sprintf("%d", *keyword.Campaign)
	} else {
		campaignID = ""
	}

	var keywordText string
	if keyword.Keyword != nil && keyword.Keyword.Text != nil {
		keywordText = *keyword.Keyword.Text
	}

	var matchType int32
	if keyword.Keyword != nil {
		matchType = int32(keyword.Keyword.MatchType)
	}

	updateData := bson.M{
		"keyword_id":   keywordID,
		"keyword_text": keywordText,
		"ad_group_id":  adGroupID,
		"campaign_id":  campaignID,

		"status":                 keyword.Status,
		"primary_status":         keyword.PrimaryStatus,
		"primary_status_reasons": keyword.PrimaryStatusReasons,

		"match_type":            matchType,
		"cpc_bid_micros":        keyword.CpcBidMicros,
		"final_urls":            keyword.FinalUrls,
		"final_mobile_urls":     keyword.FinalMobileUrls,
		"tracking_url_template": keyword.TrackingUrlTemplate,
		"final_url_suffix":      keyword.FinalUrlSuffix,
	}

	return updateData
}
