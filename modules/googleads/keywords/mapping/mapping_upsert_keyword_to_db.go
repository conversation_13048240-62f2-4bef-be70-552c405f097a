package mapping

import (
	"fmt"
	"strings"

	"github.com/shenzhencenter/google-ads-pb/resources"
	"go.mongodb.org/mongo-driver/bson"
)

func MapperUpsertKeywordToDatabase(keyword *resources.AdGroupCriterion) bson.M {
	var keywordID string
	if keyword.CriterionId != nil {
		keywordID = fmt.Sprintf("%d", *keyword.CriterionId)
	} else {
		keywordID = ""
	}

	var adGroupID string
	if keyword.AdGroup != nil {
		// AdGroup is a resource name like "customers/123/adGroups/456"
		parts := strings.Split(*keyword.AdGroup, "/adGroups/")
		if len(parts) > 1 {
			adGroupID = parts[1]
		} else {
			adGroupID = ""
		}
	} else {
		adGroupID = ""
	}

	// Basic mapping with only the fields we know exist
	updateData := bson.M{
		"keyword_id":  keywordID,
		"ad_group_id": adGroupID,
		"updated_by":  "test",
	}

	// Add optional fields if they exist
	if keyword.ResourceName != "" {
		updateData["resource_name"] = keyword.ResourceName
	}

	// Add status if available
	if keyword.Status != 0 {
		updateData["status"] = keyword.Status
	}

	return updateData
}
