package mongo

import (
	"context"
	"googledsp/modules/googleads/keywords/entity"
	"googledsp/modules/googleads/keywords/mapping"
	"time"

	"github.com/shenzhencenter/google-ads-pb/resources"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type keywordRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewKeywordRepo(DB *mongo.Database) *keywordRepo {
	return &keywordRepo{
		DB:         DB,
		Collection: DB.Collection(entity.KeywordEntity{}.CollectionName()),
	}
}

func (r *keywordRepo) BulkWriteKeywordsRepo(ctx context.Context, keywords []*resources.AdGroupCriterion) error {
	models := make([]mongo.WriteModel, 0, len(keywords))

	now := time.Now()
	for _, keyword := range keywords {
		filter := bson.M{
			"keyword_id": keyword.CriterionId,
		}

		update := bson.M{
			"$set": mapping.MapperUpsertKeywordToDatabase(keyword),
			"$setOnInsert": bson.M{
				"created_by": "test",
				"created_at": now,
			},
			"$currentDate": bson.M{
				"updated_at": true,
			},
		}

		model := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update).
			SetUpsert(true)

		models = append(models, model)
	}

	opts := options.BulkWrite().SetOrdered(false)
	_, err := r.Collection.BulkWrite(ctx, models, opts)
	return err
}

/**
 * Find ListDatatable Campaigns Aggregate
 */
// func (r *keywordRepo) FindListDatatableCampaignsRepo(ctx context.Context, pipeline []bson.M, opts ...*options.AggregateOptions) (*[]entity.CampaignEntity, error) {
// 	var campaigns []entity.CampaignEntity

// 	cursor, err := r.Collection.Aggregate(ctx, pipeline, opts...)
// 	if err != nil {
// 		return nil, err
// 	}
// 	defer cursor.Close(ctx)

// 	if cursor == nil {
// 		return nil, fberrs.ErrNilCursorValue
// 	}

// 	if err = cursor.All(ctx, &campaigns); err != nil {
// 		return nil, err
// 	}

// 	return &campaigns, nil
// }
