package api

import (
	"googledsp/modules/googleads/keywords/transport/requests"
	"net/http"

	"github.com/dev-networldasia/dspgos/sctx/core"
	"github.com/gofiber/fiber/v2"
)

func (api *ApiKeyword) ReloadKeywordApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.ReloadKeywordReq

		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if err := payload.Validate(); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if err := api.usc.ReloadKeywords(c.Context(), &payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload keywords successfully",
		}))
	}
}
