package requests

import (
	"googledsp/modules/googleads/keywords/common/errs"

	"github.com/go-playground/validator/v10"
)

type ReloadKeywordReq struct {
	AdvertiserID string `json:"advertiser_id" bson:"advertiser_id"`
}

func (req *ReloadKeywordReq) Validate() error {
	validate := validator.New()
	var validationErrors error

	err := validate.Struct(req)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			switch err.Field() {
			case "AdvertiserID":
				return errs.ErrAdvertiserIDRequired
			}
		}

		return validationErrors
	}
	return nil
}
