package entity

import (
	"googledsp/modules/googleads/keywords/common/enums"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type KeywordEntity struct {
	ID primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`

	KeywordID   string `json:"keyword_id" bson:"keyword_id"`
	KeywordText string `json:"keyword_text" bson:"keyword_text"`
	AdGroupID   string `json:"ad_group_id" bson:"ad_group_id"`
	CampaignID  string `json:"campaign_id" bson:"campaign_id"`

	Status               enums.KeywordStatusEnum_KeywordStatus                 `json:"status" bson:"status"`
	PrimaryStatus        enums.KeywordPrimaryStatusEnum_KeywordPrimaryStatus   `json:"primary_status" bson:"primary_status"`
	PrimaryStatusReasons []enums.KeywordPrimaryStatusEnum_KeywordPrimaryStatus `json:"primary_status_reasons" bson:"primary_status_reasons"`

	MatchType           enums.KeywordMatchTypeEnum_KeywordMatchType `json:"match_type" bson:"match_type"`
	CpcBidMicros        *int64                                      `json:"cpc_bid_micros,omitempty" bson:"cpc_bid_micros,omitempty"`
	FinalUrls           []string                                    `json:"final_urls,omitempty" bson:"final_urls,omitempty"`
	FinalMobileUrls     []string                                    `json:"final_mobile_urls,omitempty" bson:"final_mobile_urls,omitempty"`
	TrackingUrlTemplate *string                                     `json:"tracking_url_template,omitempty" bson:"tracking_url_template,omitempty"`
	FinalUrlSuffix      *string                                     `json:"final_url_suffix,omitempty" bson:"final_url_suffix,omitempty"`

	CreatedAt time.Time `json:"created_at" bson:"created_at"`
	CreatedBy string    `json:"created_by" bson:"created_by"`
	UpdatedAt time.Time `json:"updated_at" bson:"updated_at"`
	UpdatedBy string    `json:"updated_by" bson:"updated_by"`
}

func (KeywordEntity) CollectionName() string {
	return "googleads_keywords"
}
