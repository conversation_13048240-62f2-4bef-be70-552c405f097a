package mapping

import (
	"fmt"

	"github.com/shenzhencenter/google-ads-pb/resources"
	"go.mongodb.org/mongo-driver/bson"
)

func MapperUpsertAdToDatabase(ad *resources.Ad) bson.M {
	var adID string
	if ad.Id != nil {
		adID = fmt.Sprintf("%d", *ad.Id)
	} else {
		adID = ""
	}

	// Basic mapping with only the fields we know exist
	updateData := bson.M{
		"ad_id":   adID,
		"ad_name": ad.Name,
		// Note: AdGroup and Campaign resource names will be extracted from the resource name
		// These will be populated when we know the actual field structure
		"updated_by": "test",
	}

	// Add optional fields if they exist
	if ad.ResourceName != "" {
		updateData["resource_name"] = ad.ResourceName
	}

	return updateData
}
