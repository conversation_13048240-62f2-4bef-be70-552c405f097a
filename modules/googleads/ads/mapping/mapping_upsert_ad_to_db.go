package mapping

import (
	"fmt"

	"github.com/shenzhencenter/google-ads-pb/resources"
	"go.mongodb.org/mongo-driver/bson"
)

func MapperUpsertAdToDatabase(ad *resources.Ad) bson.M {
	var adID string
	if ad.Id != nil {
		adID = fmt.Sprintf("%d", *ad.Id)
	} else {
		adID = ""
	}

	var adGroupID string
	if ad.AdGroup != nil {
		adGroupID = fmt.Sprintf("%d", *ad.AdGroup)
	} else {
		adGroupID = ""
	}

	var campaignID string
	if ad.Campaign != nil {
		campaignID = fmt.Sprintf("%d", *ad.Campaign)
	} else {
		campaignID = ""
	}

	updateData := bson.M{
		"ad_id":        adID,
		"ad_name":      ad.Name,
		"ad_group_id":  adGroupID,
		"campaign_id":  campaignID,

		"status":                 ad.Status,
		"primary_status":         ad.PrimaryStatus,
		"primary_status_reasons": ad.PrimaryStatusReasons,

		"ad_type":               ad.Type,
		"tracking_url_template": ad.TrackingUrlTemplate,
		"final_urls":            ad.FinalUrls,
		"final_mobile_urls":     ad.FinalMobileUrls,
		"final_url_suffix":      ad.FinalUrlSuffix,
	}

	return updateData
}
