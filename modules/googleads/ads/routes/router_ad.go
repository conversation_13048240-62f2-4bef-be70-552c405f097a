package routes

import (
	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/session"
)

func SetupRoutesAd(app *fiber.App, serviceCtx sctx.ServiceContext, store *session.Store, midds ...fiber.Handler) {

	group := app.Group("dsp/googleads/ads")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				group.Use(midd)
			}
		}

		comPage := ComposerAdService(serviceCtx)
		group.Get("/list", comPage.ListAdHdl(store)).Name("googleads.ads.list")
		group.Get("/edit", comPage.EditAdHdl(store)).Name("googleads.ads.edit")
	}

	// API routes
	apiGroup := app.Group("dsp/googleads/api/ads")
	{
		if len(midds) > 0 {
			for _, midd := range midds {
				apiGroup.Use(midd)
			}
		}

		comApi := ComposerAdApiService(serviceCtx)
		apiGroup.Post("/list-table", comApi.ListTableAdsApi()).Name("googleads.api.ads.list-table")
		apiGroup.Patch("/reload", comApi.ReloadAdApi()).Name("googleads.api.ads.reload")
	}
}
