package api

import (
	"googledsp/modules/googleads/ads/transport/requests"
	"net/http"

	"github.com/dev-networldasia/dspgos/sctx/core"
	"github.com/gofiber/fiber/v2"
)

func (api *ApiAd) ReloadAdApi() fiber.Handler {
	return func(c *fiber.Ctx) error {
		var payload requests.ReloadAdReq

		if err := c.BodyParser(&payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if err := payload.Validate(); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		if err := api.usc.ReloadAds(c.Context(), &payload); err != nil {
			return core.ReturnErrForApi(c, err.Error())
		}

		return c.Status(http.StatusOK).JSON(core.ResponseData(map[string]interface{}{
			"msg": "Reload ads successfully",
		}))
	}
}
