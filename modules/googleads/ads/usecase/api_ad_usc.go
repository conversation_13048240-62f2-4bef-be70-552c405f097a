package usecase

import (
	"context"
	"fmt"
	"googledsp/modules/googleads/ads/mapping"
	"googledsp/modules/googleads/ads/transport/requests"
	"googledsp/modules/googleads/ads/transport/responses"
	"time"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/googleadsservice"
	"go.mongodb.org/mongo-driver/bson"
)

type AdUsc struct {
	service             *googleadsservice.Client
	adRepo              AdRepo
	googleAdsReportRepo GoogleAdsReportRepo
	logger              sctx.Logger
}

type ParamAdUsc struct {
	Service             *googleadsservice.Client
	AdRepo              AdRepo
	GoogleAdsReportRepo GoogleAdsReportRepo
	Logger              sctx.Logger
}

func NewAdUsc(param ParamAdUsc) *AdUsc {
	return &AdUsc{
		service:             param.Service,
		adRepo:              param.AdRepo,
		googleAdsReportRepo: param.GoogleAdsReportRepo,
		logger:              param.Logger,
	}
}

func (usc *AdUsc) getFilterAdReport(ctx context.Context, payload *requests.ListTableAdReq) bson.M {
	filter := bson.M{}

	if payload.StartTime != nil && payload.EndTime != nil {
		startTimeStr := (*payload.StartTime).Format("2006-01-02")
		endTimeStr := (*payload.EndTime).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startTimeStr, "$lte": endTimeStr}
	} else {
		yesterday := time.Now().AddDate(0, 0, -1)
		endOfYesterdayStr := yesterday.Format("2006-01-02")
		startOfYesterdayStr := yesterday.AddDate(0, 0, -30).Format("2006-01-02")
		filter["date"] = bson.M{"$gte": startOfYesterdayStr, "$lte": endOfYesterdayStr}
	}

	if payload.AdId != "" {
		filter["ad_id"] = payload.AdId
	}

	if len(payload.AdGroupIds) > 0 {
		filter["adgroup_id"] = bson.M{"$in": payload.AdGroupIds}
	} else if len(payload.CampaignIds) > 0 {
		filter["campaign_id"] = bson.M{"$in": payload.CampaignIds}
	}

	if payload.AdvertiserID != "" {
		filter["advertiser_id"] = payload.AdvertiserID
	}

	return filter
}

func (usc *AdUsc) ListTableAds(ctx context.Context, req *requests.ListTableAdReq) (*[]responses.AdRow, int64, error) {
	reportFilter := usc.getFilterAdReport(ctx, req)
	reportData, err := usc.googleAdsReportRepo.GetReportDetailByAd(ctx, reportFilter)

	seen := make(map[string]bool)
	for _, data := range *reportData {
		if seen[data.AdID] {
			fmt.Println("Duplicate AdID:", data.AdID)
		} else {
			seen[data.AdID] = true
		}
	}

	fmt.Printf("\n===== Ads len ====== %+v ", len(*reportData))
	if err != nil {
		fmt.Printf("====== Ads err ====== %+v", err)
		return nil, 0, err
	}

	dataTableRes := mapping.MapperListTableAd(reportData)

	return dataTableRes, int64(len(*dataTableRes)), nil
}

/**
 * Reload ads
 */
func (usc *AdUsc) ReloadAds(ctx context.Context, payload *requests.ReloadAdReq) error {
	// TODO: Replace with actual service method when available
	// Following the pattern: ListCampaignStream, ListAdGroup
	// Possible method names: ListAd, ListAdStream, GetAds, etc.

	usc.logger.Info("ReloadAds called for advertiser: " + payload.AdvertiserID)

	// Placeholder implementation - replace with actual service call
	// ads, err := usc.service.ListAd(ctx, payload.AdvertiserID, nil)
	// For now, return success without doing anything
	fmt.Printf("======== ReloadAds placeholder called for advertiser: %s \n", payload.AdvertiserID)

	// TODO: Uncomment and implement when service method is available
	/*
		if err != nil {
			usc.logger.Error(err)
			return err
		}

		jsonData, err := json.MarshalIndent(ads, "", "  ")
		if err != nil {
			return err
		}
		fmt.Printf("======== %+v \n", string(jsonData))

		if ads != nil && len(ads) > 0 {
			err = usc.adRepo.BulkWriteAdsRepo(ctx, ads)
			if err != nil {
				usc.logger.Error(err)
				return err
			}
		}
	*/

	return nil
}
