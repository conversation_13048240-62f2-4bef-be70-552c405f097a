package usecase

import (
	"context"
	"googledsp/modules/googleads/ads/transport/requests"
	"googledsp/modules/googleads/ads/transport/responses"
	"googledsp/modules/googleads/adserver_report/entity"

	"github.com/shenzhencenter/google-ads-pb/resources"
	"go.mongodb.org/mongo-driver/bson"
)

type AdRepo interface {
	BulkWriteAdsRepo(ctx context.Context, ads []*resources.Ad) error
}

type GoogleAdsReportRepo interface {
	GetReportDetailByAd(ctx context.Context, filter bson.M) (*[]entity.GoogleAdReportDetailEntity, error)
}

type AdUscInterface interface {
	ListTableAds(ctx context.Context, req *requests.ListTableAdReq) (*[]responses.AdRow, int64, error)
	ReloadAds(ctx context.Context, payload *requests.ReloadAdReq) error
}
