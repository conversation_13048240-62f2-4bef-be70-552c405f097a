package mongo

import (
	"context"
	"googledsp/modules/googleads/ads/entity"
	"googledsp/modules/googleads/ads/mapping"
	"time"

	"github.com/shenzhencenter/google-ads-pb/resources"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type adRepo struct {
	DB         *mongo.Database
	Collection *mongo.Collection
}

func NewAdRepo(DB *mongo.Database) *adRepo {
	return &adRepo{
		DB:         DB,
		Collection: DB.Collection(entity.AdEntity{}.CollectionName()),
	}
}

func (r *adRepo) BulkWriteAdsRepo(ctx context.Context, ads []*resources.Ad) error {
	models := make([]mongo.WriteModel, 0, len(ads))

	now := time.Now()
	for _, ad := range ads {
		filter := bson.M{
			"ad_id": ad.Id,
		}

		update := bson.M{
			"$set": mapping.MapperUpsertAdToDatabase(ad),
			"$setOnInsert": bson.M{
				"created_by": "test",
				"created_at": now,
			},
			"$currentDate": bson.M{
				"updated_at": true,
			},
		}

		model := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update).
			SetUpsert(true)

		models = append(models, model)
	}

	opts := options.BulkWrite().SetOrdered(false)
	_, err := r.Collection.BulkWrite(ctx, models, opts)
	return err
}
