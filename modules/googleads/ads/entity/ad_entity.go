package entity

import (
	"googledsp/modules/googleads/ads/common/enums"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AdEntity struct {
	ID primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`

	AdID        string `json:"ad_id" bson:"ad_id"`
	AdName      string `json:"ad_name" bson:"ad_name"`
	AdGroupID   string `json:"ad_group_id" bson:"ad_group_id"`
	CampaignID  string `json:"campaign_id" bson:"campaign_id"`

	Status               enums.AdStatusEnum_AdStatus                 `json:"status" bson:"status"`
	PrimaryStatus        enums.AdPrimaryStatusEnum_AdPrimaryStatus   `json:"primary_status" bson:"primary_status"`
	PrimaryStatusReasons []enums.AdPrimaryStatusEnum_AdPrimaryStatus `json:"primary_status_reasons" bson:"primary_status_reasons"`

	AdType              enums.AdTypeEnum_AdType `json:"ad_type" bson:"ad_type"`
	TrackingUrlTemplate *string                 `json:"tracking_url_template,omitempty" bson:"tracking_url_template,omitempty"`
	FinalUrls           []string                `json:"final_urls,omitempty" bson:"final_urls,omitempty"`
	FinalMobileUrls     []string                `json:"final_mobile_urls,omitempty" bson:"final_mobile_urls,omitempty"`
	FinalUrlSuffix      *string                 `json:"final_url_suffix,omitempty" bson:"final_url_suffix,omitempty"`

	CreatedAt time.Time `json:"created_at" bson:"created_at"`
	CreatedBy string    `json:"created_by" bson:"created_by"`
	UpdatedAt time.Time `json:"updated_at" bson:"updated_at"`
	UpdatedBy string    `json:"updated_by" bson:"updated_by"`
}

func (AdEntity) CollectionName() string {
	return "googleads_ads"
}
