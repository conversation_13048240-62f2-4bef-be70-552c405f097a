package enums

// AdStatusEnum_AdStatus represents the status of an ad
type AdStatusEnum_AdStatus int32

const (
	AdStatusEnum_UNSPECIFIED AdStatusEnum_AdStatus = 0
	AdStatusEnum_UNKNOWN     AdStatusEnum_AdStatus = 1
	AdStatusEnum_ENABLED     AdStatusEnum_AdStatus = 2
	AdStatusEnum_PAUSED      AdStatusEnum_AdStatus = 3
	AdStatusEnum_REMOVED     AdStatusEnum_AdStatus = 4
)

// AdPrimaryStatusEnum_AdPrimaryStatus represents the primary status of an ad
type AdPrimaryStatusEnum_AdPrimaryStatus int32

const (
	AdPrimaryStatusEnum_UNSPECIFIED  AdPrimaryStatusEnum_AdPrimaryStatus = 0
	AdPrimaryStatusEnum_UNKNOWN      AdPrimaryStatusEnum_AdPrimaryStatus = 1
	AdPrimaryStatusEnum_ELIGIBLE     AdPrimaryStatusEnum_AdPrimaryStatus = 2
	AdPrimaryStatusEnum_PAUSED       AdPrimaryStatusEnum_AdPrimaryStatus = 3
	AdPrimaryStatusEnum_REMOVED      AdPrimaryStatusEnum_AdPrimaryStatus = 4
	AdPrimaryStatusEnum_PENDING      AdPrimaryStatusEnum_AdPrimaryStatus = 5
	AdPrimaryStatusEnum_LIMITED      AdPrimaryStatusEnum_AdPrimaryStatus = 6
	AdPrimaryStatusEnum_NOT_ELIGIBLE AdPrimaryStatusEnum_AdPrimaryStatus = 7
)

// AdTypeEnum_AdType represents the type of an ad
type AdTypeEnum_AdType int32

const (
	// No value has been specified.
	AdTypeEnum_UNSPECIFIED AdTypeEnum_AdType = 0
	// The received value is not known in this version.
	//
	// This is a response-only value.
	AdTypeEnum_UNKNOWN AdTypeEnum_AdType = 1
	// The ad is a text ad.
	AdTypeEnum_TEXT_AD AdTypeEnum_AdType = 2
	// The ad is an expanded text ad.
	AdTypeEnum_EXPANDED_TEXT_AD AdTypeEnum_AdType = 3
	// The ad is an expanded dynamic search ad.
	AdTypeEnum_EXPANDED_DYNAMIC_SEARCH_AD AdTypeEnum_AdType = 7
	// The ad is a hotel ad.
	AdTypeEnum_HOTEL_AD AdTypeEnum_AdType = 8
	// The ad is a Smart Shopping ad.
	AdTypeEnum_SHOPPING_SMART_AD AdTypeEnum_AdType = 9
	// The ad is a standard Shopping ad.
	AdTypeEnum_SHOPPING_PRODUCT_AD AdTypeEnum_AdType = 10
	// The ad is a video ad.
	AdTypeEnum_VIDEO_AD AdTypeEnum_AdType = 12
	// This ad is an Image ad.
	AdTypeEnum_IMAGE_AD AdTypeEnum_AdType = 14
	// The ad is a responsive search ad.
	AdTypeEnum_RESPONSIVE_SEARCH_AD AdTypeEnum_AdType = 15
	// The ad is a legacy responsive display ad.
	AdTypeEnum_LEGACY_RESPONSIVE_DISPLAY_AD AdTypeEnum_AdType = 16
	// The ad is an app ad.
	AdTypeEnum_APP_AD AdTypeEnum_AdType = 17
	// The ad is a legacy app install ad.
	AdTypeEnum_LEGACY_APP_INSTALL_AD AdTypeEnum_AdType = 18
	// The ad is a responsive display ad.
	AdTypeEnum_RESPONSIVE_DISPLAY_AD AdTypeEnum_AdType = 19
	// The ad is a local ad.
	AdTypeEnum_LOCAL_AD AdTypeEnum_AdType = 20
	// The ad is a display upload ad with the HTML5_UPLOAD_AD product type.
	AdTypeEnum_HTML5_UPLOAD_AD AdTypeEnum_AdType = 21
	// The ad is a display upload ad with one of the DYNAMIC_HTML5_* product
	// types.
	AdTypeEnum_DYNAMIC_HTML5_AD AdTypeEnum_AdType = 22
	// The ad is an app engagement ad.
	AdTypeEnum_APP_ENGAGEMENT_AD AdTypeEnum_AdType = 23
	// The ad is a Shopping Comparison Listing ad.
	AdTypeEnum_SHOPPING_COMPARISON_LISTING_AD AdTypeEnum_AdType = 24
	// Video bumper ad.
	AdTypeEnum_VIDEO_BUMPER_AD AdTypeEnum_AdType = 25
	// Video non-skippable in-stream ad.
	AdTypeEnum_VIDEO_NON_SKIPPABLE_IN_STREAM_AD AdTypeEnum_AdType = 26
	// Video TrueView in-stream ad.
	AdTypeEnum_VIDEO_TRUEVIEW_IN_STREAM_AD AdTypeEnum_AdType = 29
	// Video responsive ad.
	AdTypeEnum_VIDEO_RESPONSIVE_AD AdTypeEnum_AdType = 30
	// Smart campaign ad.
	AdTypeEnum_SMART_CAMPAIGN_AD AdTypeEnum_AdType = 31
	// Call ad.
	AdTypeEnum_CALL_AD AdTypeEnum_AdType = 32
	// Universal app pre-registration ad.
	AdTypeEnum_APP_PRE_REGISTRATION_AD AdTypeEnum_AdType = 33
	// In-feed video ad.
	AdTypeEnum_IN_FEED_VIDEO_AD AdTypeEnum_AdType = 34
	// Travel ad.
	AdTypeEnum_TRAVEL_AD AdTypeEnum_AdType = 37
	// Demand Gen product ad.
	AdTypeEnum_DEMAND_GEN_PRODUCT_AD AdTypeEnum_AdType = 39
	// Demand Gen multi asset ad.
	AdTypeEnum_DEMAND_GEN_MULTI_ASSET_AD AdTypeEnum_AdType = 40
	// Demand Gen carousel ad.
	AdTypeEnum_DEMAND_GEN_CAROUSEL_AD AdTypeEnum_AdType = 41
	// Demand Gen video responsive ad.
	AdTypeEnum_DEMAND_GEN_VIDEO_RESPONSIVE_AD AdTypeEnum_AdType = 42
	// YouTube Audio ad.
	AdTypeEnum_YOUTUBE_AUDIO_AD AdTypeEnum_AdType = 44
)
